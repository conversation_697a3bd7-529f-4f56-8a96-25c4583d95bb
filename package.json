{"name": "multi-manager", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:seed": "tsx scripts/seed.ts"}, "dependencies": {"@auth/drizzle-adapter": "^1.10.0", "@aws-sdk/client-s3": "^3.846.0", "@hookform/resolvers": "^5.1.1", "@iconify/react": "^6.0.0", "@neondatabase/serverless": "^1.0.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.51.0", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-color": "^3.0.6", "@tiptap/extension-highlight": "^3.0.6", "@tiptap/extension-image": "^3.0.6", "@tiptap/extension-link": "^3.0.6", "@tiptap/extension-list-item": "^3.0.6", "@tiptap/extension-placeholder": "^3.0.6", "@tiptap/extension-table": "^3.0.6", "@tiptap/extension-table-cell": "^3.0.6", "@tiptap/extension-table-header": "^3.0.6", "@tiptap/extension-table-row": "^3.0.6", "@tiptap/extension-text-align": "^3.0.6", "@tiptap/extension-text-style": "^3.0.6", "@tiptap/extension-underline": "^3.0.6", "@tiptap/pm": "^3.0.6", "@tiptap/react": "^3.0.6", "@tiptap/starter-kit": "^3.0.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.3", "lucide-react": "^0.525.0", "next": "15.4.1", "next-auth": "5.0.0-beta.29", "next-themes": "^0.4.6", "postgres": "^3.4.7", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-table": "^7.8.0", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.0.14", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9.31.0", "eslint-config-next": "15.4.1", "tailwindcss": "^4.1.11", "tsx": "^4.20.3", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3"}}