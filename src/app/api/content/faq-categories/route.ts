import { faqCategoryTable } from "@/lib/db";
import { executeQuery } from "@/lib/db-manager";
import { desc, ilike } from "drizzle-orm";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get("projectId");
    const search = searchParams.get("search") || "";

    if (!projectId) {
      return NextResponse.json(
        {
          success: false,
          error: "缺少项目ID参数",
        },
        { status: 400 }
      );
    }

    // 使用executeQuery统一处理数据库连接和查询
    const categories = await executeQuery(projectId, async (businessDb) => {
      const trimedSearch = search ? search.trim() : "";
      const condition = trimedSearch
        ? ilike(faqCategoryTable.name, `%${trimedSearch}%`)
        : undefined;
      return await businessDb
        .select()
        .from(faqCategoryTable)
        .where(condition)
        .orderBy(faqCategoryTable.sort, desc(faqCategoryTable.createdAt));
    });

    return NextResponse.json({
      success: true,
      data: categories,
    });
  } catch (error) {
    console.error("Get categories error:", error);
    return NextResponse.json(
      {
        success: false,
        error: "获取FAQ分类列表失败",
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { projectId, name, slug, description, sort, isActive } = body;

    if (!projectId) {
      return NextResponse.json(
        {
          success: false,
          error: "缺少项目ID参数",
        },
        { status: 400 }
      );
    }

    const [category] = await executeQuery(projectId, async (businessDb) => {
      return await businessDb
        .insert(faqCategoryTable)
        .values({
          slug: slug,
          name,
          description,
          sort,
          isActive: isActive ?? true,
        })
        .returning();
    });
    return NextResponse.json({
      success: true,
      data: category,
    });
  } catch (error) {
    console.error("Create FAQ category error:", error);
  }
}
