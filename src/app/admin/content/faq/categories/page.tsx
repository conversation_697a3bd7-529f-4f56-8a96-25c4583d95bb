"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { PageHeaderWithProject } from '@/components/ui/page-header-with-project';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Plus,
  Edit,
  Trash2,
  <PERSON>L<PERSON><PERSON>,
  Loader2,
  AlertCircle,
  Database,
  FolderOpen
} from 'lucide-react';
import { Project } from '@/types/project';
import { FaqCategoryType } from '@/types/faq';

interface CategoryFormData {
  name: string;
  slug: string;
  description: string;
  sort: number;
  isActive: boolean;
}

export default function FaqCategoriesPage() {
  const searchParams = useSearchParams();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<FaqCategoryType[]>([]);
  
  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<FaqCategoryType | null>(null);
  const [formData, setFormData] = useState<CategoryFormData>({
    name: '',
    slug: '',
    description: '',
    sort: 0,
    isActive: true,
  });
  const [formLoading, setFormLoading] = useState(false);

  const handleProjectChange = (projectId: number, project: Project) => {
    setSelectedProject(project);
    loadCategories(projectId);
  };

  useEffect(() => {
    const projectId = searchParams.get('projectId');
    if (projectId) {
      // 这里应该根据projectId获取项目信息，暂时模拟
      const mockProject: Project = {
        id: parseInt(projectId),
        name: '当前项目',
        description: '',
        domain: '',
        dbHost: '',
        dbPort: 5432,
        dbName: '',
        dbUser: '',
        isActive: true,
        config: { features: [] },
        createdAt: new Date().toISOString(),
      };
      setSelectedProject(mockProject);
      loadCategories(parseInt(projectId));
    }
  }, [searchParams]);

  const loadCategories = async (projectId: number) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/content/faq-categories?projectId=${projectId}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setCategories(result.data || []);
        } else {
          setError(result.error || "获取分类数据失败");
        }
      } else {
        setError("网络请求失败");
      }
    } catch (err) {
      setError("加载分类数据时出错");
      console.error("Load categories error:", err);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      slug: '',
      description: '',
      sort: 0,
      isActive: true,
    });
    setEditingCategory(null);
  };

  const handleCreate = () => {
    resetForm();
    setIsCreateDialogOpen(true);
  };

  const handleEdit = (category: FaqCategoryType) => {
    setFormData({
      name: category.name,
      slug: category.slug,
      description: category.description || '',
      sort: category.sort || 0,
      isActive: category.isActive ?? true,
    });
    setEditingCategory(category);
    setIsEditDialogOpen(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedProject) return;

    setFormLoading(true);
    try {
      const url = editingCategory 
        ? `/api/content/faq-categories/${editingCategory.id}?projectId=${selectedProject.id}`
        : `/api/content/faq-categories?projectId=${selectedProject.id}`;
      
      const method = editingCategory ? 'PUT' : 'POST';
      const body = editingCategory 
        ? formData
        : { ...formData, projectId: selectedProject.id };

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          loadCategories(selectedProject.id);
          setIsCreateDialogOpen(false);
          setIsEditDialogOpen(false);
          resetForm();
        } else {
          setError(result.error || "操作失败");
        }
      } else {
        setError("网络请求失败");
      }
    } catch (err) {
      setError("操作时出错");
      console.error("Submit error:", err);
    } finally {
      setFormLoading(false);
    }
  };

  const handleDelete = async (categoryId: number) => {
    if (!selectedProject || !confirm('确定删除这个分类吗？')) return;
    
    try {
      const response = await fetch(`/api/content/faq-categories/${categoryId}?projectId=${selectedProject.id}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        loadCategories(selectedProject.id);
      } else {
        alert('删除失败');
      }
    } catch (err) {
      console.error('Delete category error:', err);
      alert('删除时出错');
    }
  };

  // 自动生成slug
  const handleNameChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      name: value,
      slug: value.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-')
    }));
  };

  return (
    <div className="space-y-6">
      {/* 页面头部组件 */}
      <PageHeaderWithProject
        title="FAQ分类管理"
        description="管理FAQ分类、编辑分类信息"
        selectedProject={selectedProject}
        onProjectChange={handleProjectChange}
        actions={
          <div className="flex gap-2">
            <Button asChild variant="outline">
              <Link href={`/admin/content/faq?projectId=${selectedProject?.id}`}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                返回FAQ管理
              </Link>
            </Button>
            {selectedProject && (
              <Button onClick={handleCreate}>
                <Plus className="mr-2 h-4 w-4" />
                新建分类
              </Button>
            )}
          </div>
        }
      />

      {!selectedProject ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center text-muted-foreground">
              <Database className="h-12 w-12 mx-auto mb-4" />
              <p>请先选择一个项目来管理FAQ分类</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* 错误提示 */}
          {error && (
            <Card className="border-destructive">
              <CardContent className="flex items-center gap-2 py-4">
                <AlertCircle className="h-5 w-5 text-destructive" />
                <span className="text-destructive">{error}</span>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => selectedProject && loadCategories(selectedProject.id)}
                  className="ml-auto"
                >
                  重试
                </Button>
              </CardContent>
            </Card>
          )}

          {/* 分类列表 */}
          <Card>
            <CardHeader>
              <CardTitle>分类列表</CardTitle>
              <CardDescription>
                {loading ? '加载中...' : `共 ${categories.length} 个分类`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                    <p className="text-muted-foreground">加载分类列表中...</p>
                  </div>
                </div>
              ) : categories.length === 0 ? (
                <div className="text-center py-12 text-muted-foreground">
                  <FolderOpen className="h-12 w-12 mx-auto mb-4" />
                  <p className="text-lg font-medium mb-2">暂无分类</p>
                  <p>开始创建您的第一个FAQ分类吧</p>
                  <Button onClick={handleCreate} className="mt-4">
                    <Plus className="mr-2 h-4 w-4" />
                    新建分类
                  </Button>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>分类名称</TableHead>
                      <TableHead>标识符</TableHead>
                      <TableHead>描述</TableHead>
                      <TableHead>排序</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {categories.map((category) => (
                      <TableRow key={category.id}>
                        <TableCell>
                          <div className="font-medium">
                            {category.name}
                          </div>
                        </TableCell>
                        <TableCell>
                          <code className="text-sm bg-muted px-1 py-0.5 rounded">
                            {category.slug}
                          </code>
                        </TableCell>
                        <TableCell>
                          <div className="max-w-xs truncate text-muted-foreground">
                            {category.description || '-'}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm font-mono">
                            {category.sort}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={category.isActive ? "default" : "secondary"}>
                            {category.isActive ? '启用' : '禁用'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {category.createdAt?.toLocaleString()}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center gap-1 justify-end">
                            <Button variant="ghost" size="sm" onClick={() => handleEdit(category)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm" onClick={() => handleDelete(category.id)}>
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </>
      )}

      {/* 新建分类Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>新建分类</DialogTitle>
            <DialogDescription>
              创建一个新的FAQ分类
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">分类名称</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  placeholder="请输入分类名称"
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="slug">标识符</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                  placeholder="自动生成或手动输入"
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">描述</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="请输入分类描述（可选）"
                  rows={3}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="sort">排序</Label>
                <Input
                  id="sort"
                  type="number"
                  value={formData.sort}
                  onChange={(e) => setFormData(prev => ({ ...prev, sort: parseInt(e.target.value) || 0 }))}
                  placeholder="排序值，数字越小越靠前"
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="isActive">启用状态</Label>
                  <div className="text-sm text-muted-foreground">
                    启用后该分类将在前端显示
                  </div>
                </div>
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked: boolean) => setFormData(prev => ({ ...prev, isActive: checked }))}
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                取消
              </Button>
              <Button type="submit" disabled={formLoading}>
                {formLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    创建中...
                  </>
                ) : (
                  '创建分类'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* 编辑分类Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>编辑分类</DialogTitle>
            <DialogDescription>
              修改分类信息
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-name">分类名称</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  placeholder="请输入分类名称"
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-slug">标识符</Label>
                <Input
                  id="edit-slug"
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                  placeholder="自动生成或手动输入"
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-description">描述</Label>
                <Textarea
                  id="edit-description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="请输入分类描述（可选）"
                  rows={3}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-sort">排序</Label>
                <Input
                  id="edit-sort"
                  type="number"
                  value={formData.sort}
                  onChange={(e) => setFormData(prev => ({ ...prev, sort: parseInt(e.target.value) || 0 }))}
                  placeholder="排序值，数字越小越靠前"
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="edit-isActive">启用状态</Label>
                  <div className="text-sm text-muted-foreground">
                    启用后该分类将在前端显示
                  </div>
                </div>
                <Switch
                  id="edit-isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked: boolean) => setFormData(prev => ({ ...prev, isActive: checked }))}
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                取消
              </Button>
              <Button type="submit" disabled={formLoading}>
                {formLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    保存中...
                  </>
                ) : (
                  '保存修改'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
